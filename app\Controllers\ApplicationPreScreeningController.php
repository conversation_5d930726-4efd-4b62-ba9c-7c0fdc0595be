<?php

namespace App\Controllers;

class ApplicationPreScreeningController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = \Config\Services::session();
    }

    /**
     * [GET] Display exercises in selection status for pre-screening context.
     * URI: /application_pre_screening/exercises
     */
    public function exercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load ExerciseModel and get exercises where status != 'draft'
        $exerciseModel = new \App\Models\ExerciseModel();
        $exercises = $exerciseModel->getPreScreeningExercises($orgId);

        $data = [
            'title' => 'Exercises Available for Pre-Screening',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/application_pre_screening_exercise_list', $data);
    }

    /**
     * [GET] Display unique applicants within an exercise for pre-screening.
     * URI: /application_pre_screening/exercise/{exerciseId}/prescreening_applicants
     */
    public function exercisePreScreeningApplicants($exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get unique applicants for this exercise
        $applicants = $applicationModel->select('
                appx_application_details.applicant_id,
                appx_application_details.first_name,
                appx_application_details.last_name,
                appx_application_details.email_address,
                appx_application_details.gender,
                appx_application_details.contact_details,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name,
                COUNT(appx_application_details.id) as application_count
            ')
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->groupBy('applicant_id')
            ->orderBy('full_name', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Applicants in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicants' => $applicants
        ];

        return view('application_pre_screening/exercise_prescreening_applicants_list', $data);
    }

    /**
     * [GET] Display pre-screening profile for a specific applicant within an exercise.
     * URI: /application_pre_screening/prescreening_profile/{applicantId}/{exerciseId}
     */
    public function preScreeningProfile($applicantId, $exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();
        $applicantModel = new \App\Models\ApplicantsModel();
        $applicationFilesModel = new \App\Models\AppxApplicationFilesModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get applicant basic details
        $applicant = $applicantModel->find($applicantId);
        if (!$applicant) {
            $this->session->setFlashdata('error', 'Applicant not found.');
            return redirect()->to(base_url('application_pre_screening/exercise/' . $exerciseId . '/prescreening_applicants'));
        }

        // Get all applications for this applicant in this exercise with position details
        $applications = $applicationModel->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions.annual_salary,
                positions_groups.group_name,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('appx_application_details.applicant_id', $applicantId)
            ->where('appx_application_details.exercise_id', $exerciseId)
            ->where('appx_application_details.org_id', $orgId)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        if (empty($applications)) {
            $this->session->setFlashdata('error', 'No applications found for this applicant in this exercise.');
            return redirect()->to(base_url('application_pre_screening/exercise/' . $exerciseId . '/prescreening_applicants'));
        }

        // Get applicant name from first application (more reliable than applicant table)
        $applicantName = $applications[0]['full_name'];

        // Get all files for this applicant across all applications in this exercise
        $applicantFiles = [];
        $uniqueFiles = []; // Track unique files to avoid duplicates

        foreach ($applications as $application) {
            $files = $applicationFilesModel->getFilesByApplicationId($application['id']);
            foreach ($files as $file) {
                // Create a unique identifier for the file
                // Priority: 1) applicant_file_id, 2) file_path, 3) file_title + applicant_id
                $uniqueKey = '';

                if (!empty($file['applicant_file_id'])) {
                    // Use applicant_file_id as primary unique identifier
                    $uniqueKey = 'file_id_' . $file['applicant_file_id'];
                } elseif (!empty($file['file_path'])) {
                    // Use file_path as secondary unique identifier
                    $uniqueKey = 'file_path_' . $file['file_path'];
                } else {
                    // Fallback to file_title + applicant_id
                    $uniqueKey = 'file_title_' . $file['applicant_id'] . '_' . $file['file_title'];
                }

                // Only add if we haven't seen this unique file before
                if (!isset($uniqueFiles[$uniqueKey])) {
                    $uniqueFiles[$uniqueKey] = true;
                    $applicantFiles[] = $file;
                }
            }
        }

        // Get pre-screening criteria from exercise
        $preScreenCriteria = [];
        if (!empty($exercise['pre_screen_criteria'])) {
            $preScreenCriteria = json_decode($exercise['pre_screen_criteria'], true) ?? [];
        }

        $data = [
            'title' => 'Pre-Screening Profile: ' . esc($applicantName),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicant' => $applicant,
            'applicant_name' => $applicantName,
            'applicant_id' => $applicantId,
            'applications' => $applications,
            'applicant_files' => $applicantFiles,
            'pre_screen_criteria' => $preScreenCriteria
        ];

        return view('application_pre_screening/prescreening_profile', $data);
    }

    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_pre_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get application with related data
        $application = $applicationModel->getApplicationWithDetails($id, $orgId);

        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Extract exercise data from application
        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => $application['exercise_name'],
            'gazzetted_no' => $application['gazzetted_no'],
            'advertisement_no' => $application['advertisement_no'],
            'gazzetted_date' => $application['gazzetted_date'],
            'advertisement_date' => $application['advertisement_date'],
            'is_internal' => $application['is_internal'],
            'mode_of_advertisement' => $application['mode_of_advertisement'],
            'publish_date_from' => $application['publish_date_from'],
            'publish_date_to' => $application['publish_date_to']
        ];

        // Extract position data from application
        $position = [
            'id' => $application['position_id'],
            'position_reference' => $application['position_reference'],
            'designation' => $application['position_title'],
            'classification' => $application['classification'],
            'location' => $application['position_location'],
            'annual_salary' => $application['annual_salary'],
            'qualifications' => $application['qualifications'],
            'knowledge' => $application['knowledge'],
            'skills_competencies' => $application['skills_competencies'],
            'job_experiences' => $application['job_experiences']
        ];

        // Extract pre-screening criteria from exercise if available
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
        }

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'exercise' => $exercise,
            'position' => $position,
            'preScreenCriteria' => $preScreenCriteria
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_pre_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load model
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Find the application first to ensure it exists
        $application = $applicationModel->getApplicationWithDetails($id, $orgId);
        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Process criteria results
        $criteriaIndices = $this->request->getPost('criteria_indices') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];

        $criteriaResults = [];
        foreach ($criteriaIndices as $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update
        $data = [
            'pre_screened_at' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id')
        ];

        // Update the application
        if ($applicationModel->update($id, $data)) {
            return redirect()->to(base_url('application_pre_screening/show/' . $id))
                ->with('success', 'Pre-screening results saved successfully.');
        } else {
            return redirect()->to(base_url('application_pre_screening/show/' . $id))
                ->with('error', 'Failed to save pre-screening results.');
        }
    }

    /**
     * [POST] Batch pre-screen multiple applications.
     * URI: /application_pre_screening/batch_update
     */
    public function batchUpdate()
    {
        $ids = $this->request->getPost('ids');
        $status = $this->request->getPost('status');
        $remarks = trim($this->request->getPost('remarks') ?? '');

        if (empty($ids) || !is_array($ids)) {
            $this->session->setFlashdata('error', 'No applications selected.');
            return redirect()->back();
        }

        if (empty($status)) {
            $this->session->setFlashdata('error', 'Status is required.');
            return redirect()->back()->withInput();
        }

        if ($status === 'failed' && empty($remarks)) {
             $this->session->setFlashdata('error', 'Remarks are required when batch failing applications.');
            return redirect()->back()->withInput();
        }

        // Load model
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Prepare batch update data
        $data = [
            'pre_screened_at' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $status,
            'pre_screened_remarks' => "[Batch Update] " . $remarks,
            'pre_screened_criteria_results' => null,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id')
        ];

        $successCount = 0;
        $failCount = 0;

        foreach ($ids as $id) {
            $id = (int) $id;
            if ($id > 0) {
                try {
                    if ($applicationModel->update($id, $data)) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                } catch (\Exception $e) {
                    log_message('error', 'Exception during batch pre-screening for ID ' . $id . ': ' . $e->getMessage());
                    $failCount++;
                }
            } else {
                $failCount++;
            }
        }

        // Set Flash Message
        $message = '';
        if ($successCount > 0) {
            $message .= "$successCount application(s) pre-screened successfully. ";
        }
        if ($failCount > 0) {
            $message .= "$failCount application(s) failed to update.";
        }

        if ($successCount > 0) {
            $this->session->setFlashdata('success', $message);
        } else {
            $this->session->setFlashdata('error', $message);
        }

        return redirect()->back();
    }

    /**
     * [GET] Display all applications for a specific applicant within an exercise.
     * URI: /application_pre_screening/applicant_applications/{applicantId}/{exerciseId}
     */
    public function applicantApplications($applicantId, $exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get all applications for this applicant in this exercise
        $applications = $applicationModel->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions_groups.group_name,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('appx_application_details.applicant_id', $applicantId)
            ->where('appx_application_details.exercise_id', $exerciseId)
            ->where('appx_application_details.org_id', $orgId)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        if (empty($applications)) {
            $this->session->setFlashdata('error', 'No applications found for this applicant in this exercise.');
            return redirect()->to(base_url('application_pre_screening/exercise/' . $exerciseId . '/prescreening_applicants'));
        }

        // Get applicant name from first application
        $applicantName = $applications[0]['full_name'];

        $data = [
            'title' => 'Applications by ' . esc($applicantName) . ' in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicant_name' => $applicantName,
            'applicant_id' => $applicantId,
            'applications' => $applications
        ];

        return view('application_pre_screening/applicant_applications_list', $data);
    }

    /**
     * [POST] Save pre-screening results from the prescreening profile page.
     * URI: /application_pre_screening/save_prescreening_results
     */
    public function save_prescreening_results()
    {
        // Set JSON response header
        $this->response->setContentType('application/json');

        try {
            $orgId = $this->session->get('org_id');
            if (!$orgId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Organization context not found.'
                ]);
            }

            // Get form data
            $applicantId = $this->request->getPost('applicant_id');
            $exerciseId = $this->request->getPost('exercise_id');
            $status = $this->request->getPost('pre_screened_status');
            $remarks = $this->request->getPost('pre_screened_remarks');
            $aiAnalysis = $this->request->getPost('pre_screened_ai_analysis');
            $criteriaResults = $this->request->getPost('pre_screened_criteria_results');

            // Validate required fields
            if (empty($applicantId) || empty($exerciseId) || empty($status)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Missing required fields: applicant_id, exercise_id, or status.'
                ]);
            }

            // Load model
            $applicationModel = new \App\Models\AppxApplicationDetailsModel();

            // Find the application to update
            $application = $applicationModel->where('applicant_id', $applicantId)
                                          ->where('exercise_id', $exerciseId)
                                          ->where('org_id', $orgId)
                                          ->first();

            if (!$application) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Application not found for this applicant and exercise.'
                ]);
            }

            // Prepare data for update
            $updateData = [
                'pre_screened_at' => date('Y-m-d H:i:s'),
                'pre_screened_by' => $this->session->get('user_id'),
                'pre_screened_status' => $status,
                'pre_screened_remarks' => trim($remarks ?? ''),
                'pre_screened_ai_analysis' => $aiAnalysis,
                'pre_screened_criteria_results' => $criteriaResults,
                'updated_by' => $this->session->get('user_id')
            ];

            // Update the application
            if ($applicationModel->update($application['id'], $updateData)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Pre-screening results saved successfully.',
                    'data' => [
                        'application_id' => $application['id'],
                        'pre_screened_at' => $updateData['pre_screened_at'],
                        'pre_screened_status' => $status
                    ]
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save pre-screening results to database.',
                    'errors' => $applicationModel->errors()
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error saving pre-screening results: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while saving pre-screening results: ' . $e->getMessage()
            ]);
        }
    }
}
