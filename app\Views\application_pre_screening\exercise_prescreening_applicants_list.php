<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening') ?>">Pre-Screening</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pre-Screening Applicants</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('application_pre_screening') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Exercises
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Exercise Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-body">
                <h5 class="card-title text-red">Exercise Information</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Exercise Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                        <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Status:</strong> 
                            <span class="badge bg-<?= $exercise['status'] === 'published' ? 'success' : 'warning' ?>">
                                <?= ucfirst(esc($exercise['status'])) ?>
                            </span>
                        </p>
                        <p><strong>Total Unique Applicants:</strong> 
                            <span class="badge bg-primary"><?= count($applicants) ?></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pre-Screening Applicants List -->
<div class="row">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> Pre-Screening Applicants List
                </h5>
            </div>
            <div class="card-body">
                <!-- Information Note -->
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> This list shows only applicants who applied for positions marked for pre-screening.
                </div>

                <?php if (empty($applicants)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-4x text-muted mb-4"></i>
                        <h5 class="text-muted">No Pre-Screening Applicants Found</h5>
                        <p class="text-muted">No applicants have applied for positions marked for pre-screening in this exercise yet.</p>
                        <p class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Only applicants who applied for positions marked for pre-screening are shown here.
                            </small>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="applicantsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Applicant ID</th>
                                    <th scope="col">Full Name</th>
                                    <th scope="col">Email Address</th>
                                    <th scope="col">Gender</th>
                                    <th scope="col">Contact Details</th>
                                    <th scope="col">Applications Count</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($applicants as $index => $applicant): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?= esc($applicant['applicant_id']) ?></span>
                                        </td>
                                        <td>
                                            <strong class="text-red"><?= esc($applicant['full_name']) ?></strong>
                                        </td>
                                        <td>
                                            <a href="mailto:<?= esc($applicant['email_address']) ?>" class="text-decoration-none text-red">
                                                <?= esc($applicant['email_address']) ?>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $applicant['gender'] === 'Male' ? 'info' : 'warning' ?>">
                                                <?= esc($applicant['gender']) ?>
                                            </span>
                                        </td>
                                        <td><?= esc($applicant['contact_details']) ?></td>
                                        <td>
                                            <span class="badge bg-success"><?= esc($applicant['application_count']) ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <a href="<?= base_url('application_pre_screening/prescreening_profile/' . $applicant['applicant_id'] . '/' . $exercise['id']) ?>"
                                                   class="btn btn-sm btn-outline-primary" title="View Pre-Screening Profile">
                                                    <i class="fas fa-user-check"></i> View Profile
                                                </a>
                                                <a href="<?= base_url('application_pre_screening/applicant_applications/' . $applicant['applicant_id'] . '/' . $exercise['id']) ?>"
                                                   class="btn btn-sm btn-outline-success" title="View Applications">
                                                    <i class="fas fa-file-alt"></i> View Applications
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Summary Information -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info border-0">
                                <i class="fas fa-info-circle text-red"></i>
                                <strong>Summary:</strong> 
                                This list shows <?= count($applicants) ?> unique applicants who have applied for positions in this exercise. 
                                Each applicant is listed only once, regardless of how many positions they applied for.
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicantsTable').DataTable({
        "pageLength": 25,
        "order": [[ 2, "asc" ]], // Sort by Full Name
        "columnDefs": [
            { "orderable": false, "targets": [7] } // Disable sorting on Actions column
        ],
        "language": {
            "search": "Search Applicants:",
            "lengthMenu": "Show _MENU_ applicants per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ applicants",
            "infoEmpty": "No applicants found",
            "infoFiltered": "(filtered from _MAX_ total applicants)"
        }
    });
});
</script>
<?= $this->endSection() ?>
