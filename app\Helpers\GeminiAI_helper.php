<?php

// Remove the GuzzleHttp dependency
// use GuzzleHttp\Client;

/**
 * Helper file for Gemini API integration
 * This file provides functions to interact with Google's Gemini AI API
 */

if (!function_exists('gemini_generate_profile')) {
    /**
     * Generate a comprehensive profile using Google's Gemini AI API
     * 
     * @param array $data The data to send to Gemini AI
     * @param string $apiKey The Gemini API key
     * @return array Response with success status and generated content or error message
     */
    function gemini_generate_profile(array $data, string $apiKey = null)
    {
        // Use the default API key if not provided
        if (empty($apiKey)) {
            // Use hardcoded API key for simplicity
            $apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        }

        // Initialize cURL
        $curl = curl_init();

        // Prepare the request body
        $requestBody = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => "Generate a comprehensive professional profile for this applicant based on the information provided. The profile should follow this exact structure:

I. Core Identifiers & Contact Information:
- Name: Full name of the applicant.
- Date of Birth and Age: Applicant's age.
- Sex: Male/Female.
- Address/Location: Current residential or work location.
- Contact Details: Phone number(s), Email address(es).
- Place of Origin: Village, District, Province.
- NID Number: National Identification number.

II. Employment Information:
- Current Position: Job title of the applicant's current role.
- Current Employer: Name of the organization the applicant currently works for.
- Public Service Status: Whether the applicant is currently a public servant or not.

III. Education & Training:
- Qualifications: Formal educational achievements (e.g., Degrees, Diplomas, Certificates - compared against Job Description).
- Other Training/Courses Attended: Additional relevant professional development, workshops, certifications etc.

IV. Knowledge, Skills, and Competencies:
- Knowledge: Specific areas of expertise, technical understanding, theoretical knowledge relevant to the position.
- Skills/Competencies: Practical abilities, technical skills (e.g., computer literacy), soft skills (e.g., leadership, communication, planning).

V. Experience:
- Related Job Experience: Detailed history of previous roles, responsibilities, organizations, and durations, focusing on relevance to the target position.

VI. Performance & Achievements:
- Performance Level: Assessment of past job performance or potential based on experience.
- Publications: Any relevant published work.
- Awards: Any relevant awards or recognitions received.

VII. Verification (Supporting Information):
- Referees: Contact details for professional references.

For each section, use ONLY the data provided. DO NOT invent or assume additional information. If data for a section is missing, simply state 'Information not available' for that section.

The profile should be professionally written, concise, and highlight the most important aspects of the candidate's background and potential fit for the position. Use a formal tone throughout.

Focus especially on comparing the applicant's qualifications against the job requirements and highlighting relevant experience and skills for the position they've applied for.

Here is the applicant data in a structured format:\n\n" . json_encode($data, JSON_PRETTY_PRINT)
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.3,
                'topK' => 32,
                'topP' => 0.95,
                'maxOutputTokens' => 4096,
                'responseMimeType' => 'text/plain'
            ]
        ];

        // Set cURL options with improved configuration
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" . $apiKey,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60, // 1 minute timeout
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($requestBody),
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json"
            ],
        ]);

        // Execute the request
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        // Handle the response
        if ($err) {
            log_message('error', 'Gemini API Error: ' . $err);
            return [
                'success' => false,
                'message' => 'Error connecting to Gemini API: ' . $err
            ];
        }

        // Parse the response
        $responseData = json_decode($response, true);

        if ($statusCode !== 200 || empty($responseData) || isset($responseData['error'])) {
            $errorMessage = isset($responseData['error']['message']) 
                ? $responseData['error']['message'] 
                : 'Unknown error with Gemini API';
            
            log_message('error', 'Gemini API Response Error: ' . $errorMessage);
            
            return [
                'success' => false,
                'message' => 'Error from Gemini API: ' . $errorMessage
            ];
        }

        // Check if we have text in the response
        if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'profile' => $responseData['candidates'][0]['content']['parts'][0]['text']
            ];
        }

        // Fallback error if response structure isn't as expected
        return [
            'success' => false,
            'message' => 'Unexpected response structure from Gemini API'
        ];
    }
}

function generate_profile($data) {
    if (empty($data)) {
        return "No data provided for profile generation.";
    }

    // Settings for Gemini API
    $apiKey = getenv('GEMINI_API_KEY');
    if (empty($apiKey)) {
        return "API key is not configured. Please set the GEMINI_API_KEY environment variable.";
    }

    // Use cURL instead of Guzzle client
    $curl = curl_init();
    $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" . $apiKey;

    // Build a prompt for generating a professional profile
    $prompt = "You are a professional HR assistant. Generate a detailed employee profile report based on the following structured information. Use only the data provided:

**STRUCTURED EMPLOYEE PROFILE REPORT**

This report should follow this structure with 7 main sections. For each section, use only the data provided. If information is not available for any specific section, state 'Information not available' rather than making assumptions:

I. Core Identifiers & Contact Information
- Full name 
- Date of birth and age
- Sex
- Address/location
- Contact details
- Place of origin
- National ID number
- Citizenship
- Include spouse information, children, or offense conviction information if provided

II. Employment Information
- Current position
- Current employer
- Current salary
- Public service status

III. Education & Training
- Formal education qualifications (list all provided)
- Position applied for (title and requirements)
- Training courses attended (list all with dates)

IV. Knowledge, Skills, and Competencies
- Knowledge areas (derived from education and experience)
- Skills and competencies (derived from work descriptions and achievements)
- Analyze how these match the requirements of the position applied for

V. Experience
- Related job experience (chronologically ordered)
- Experience summary (total years, positions, earliest experience)
- Highlight public service experience specifically

VI. Performance & Achievements
- Performance level (if available)
- Publications (if available)
- Awards and recognitions (if available)

VII. Verification
- References provided

Write professionally and formally. Be concise but comprehensive. Format the profile in a clean, section-by-section structure with proper headings and bullet points where appropriate. 

The profile should be suitable for HR review in a government recruitment context. Focus on presenting factual information without subjective judgment.";

    // Prepare the request payload
    $requestBody = [
        'contents' => [
            [
                'role' => 'user',
                'parts' => [
                    [
                        'text' => $prompt . "\n\nData: " . json_encode($data, JSON_PRETTY_PRINT)
                    ]
                ]
            ]
        ],
        'generationConfig' => [
            'temperature' => 0.3,
            'topK' => 32,
            'topP' => 0.95,
            'maxOutputTokens' => 4096,
            'responseMimeType' => 'text/plain'
        ]
    ];

    try {
        // Set cURL options
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60, // 1 minute timeout
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($requestBody),
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json"
            ],
        ]);

        // Execute the request
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        // Handle any cURL errors
        if ($err) {
            log_message('error', 'Gemini API Error: ' . $err);
            return "Error connecting to Gemini API: " . $err;
        }

        // Parse the response
        $result = json_decode($response, true);
        
        if ($statusCode !== 200 || empty($result) || isset($result['error'])) {
            $errorMessage = isset($result['error']['message']) 
                ? $result['error']['message'] 
                : 'Unknown error with Gemini API';
            
            log_message('error', 'Gemini API Response Error: ' . $errorMessage);
            return "Error from Gemini API: " . $errorMessage;
        }
        
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return $result['candidates'][0]['content']['parts'][0]['text'];
        } else {
            return "Error: Unable to generate profile. Response format not as expected.";
        }
    } catch (\Exception $e) {
        return "Error generating profile: " . $e->getMessage();
    }
}

if (!function_exists('gemini_extract_text_from_file')) {
    /**
     * Smart Gemini AI text extraction from file with page splitting for large documents
     *
     * @param string $filePath Full path to the file
     * @param string $processId Optional process ID for progress tracking
     * @return array Response with extracted text or error
     */
    function gemini_extract_text_from_file($filePath, $processId = null)
    {
        // Gemini API configuration
        $apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        $baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        $model = 'gemini-2.0-flash';

        if (empty($apiKey)) {
            return [
                'success' => false,
                'message' => 'Gemini API key not configured'
            ];
        }

        // Check if file exists
        if (!file_exists($filePath)) {
            return [
                'success' => false,
                'message' => 'File not found: ' . $filePath
            ];
        }

        // Get file info
        $fileSize = filesize($filePath);
        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

        // Check file size (max 25MB)
        if ($fileSize > 25 * 1024 * 1024) {
            return [
                'success' => false,
                'message' => 'File too large. Maximum size is 25MB.'
            ];
        }

        // Check if file type is supported
        $supportedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/webp',
            'text/plain'
        ];

        if (!in_array($mimeType, $supportedTypes)) {
            return [
                'success' => false,
                'message' => 'Unsupported file type: ' . $mimeType
            ];
        }

        try {
            // For PDF files, check page count and split if necessary
            if ($mimeType === 'application/pdf') {
                return gemini_extract_text_from_pdf_with_splitting($filePath, $apiKey, $baseUrl, $model, $processId);
            }

            // For non-PDF files, use direct extraction
            return gemini_extract_text_direct($filePath, $mimeType, $apiKey, $baseUrl, $model, $processId);

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error processing file: ' . $e->getMessage()
            ];
        }
    }
}

if (!function_exists('gemini_extract_text_from_pdf_with_splitting')) {
    /**
     * Extract text from PDF with page splitting for documents > 10 pages
     *
     * @param string $filePath Full path to the PDF file
     * @param string $apiKey Gemini API key
     * @param string $baseUrl Gemini API base URL
     * @param string $model Gemini model name
     * @param string $processId Optional process ID for progress tracking
     * @return array Response with extracted text or error
     */
    function gemini_extract_text_from_pdf_with_splitting($filePath, $apiKey, $baseUrl, $model, $processId = null)
    {
        try {
            // Update progress
            gemini_update_progress($processId, 45, 'analyzing', 'Analyzing PDF structure...');

            // Count PDF pages using smalot/pdfparser
            $parser = new \Smalot\PdfParser\Parser();
            $pdf = $parser->parseFile($filePath);
            $pages = $pdf->getPages();
            $totalPages = count($pages);

            log_message('info', "PDF has {$totalPages} pages: " . basename($filePath));

            // If 10 pages or less, process directly
            if ($totalPages <= 10) {
                log_message('info', "PDF has {$totalPages} pages, processing directly");
                gemini_update_progress($processId, 50, 'extracting', "Processing {$totalPages} pages with AI...");
                return gemini_extract_text_direct($filePath, 'application/pdf', $apiKey, $baseUrl, $model, $processId);
            }

            // Split PDF into chunks of max 10 pages each
            log_message('info', "PDF has {$totalPages} pages, splitting into chunks");
            gemini_update_progress($processId, 50, 'splitting', "Splitting {$totalPages} pages into chunks...");

            $chunks = gemini_split_pdf_into_chunks($filePath, $totalPages, 10);

            if (!$chunks['success']) {
                return $chunks;
            }

            $allExtractedTexts = [];
            $totalTextLength = 0;
            $totalWordCount = 0;
            $chunkFiles = $chunks['chunk_files'];
            $totalChunks = count($chunkFiles);

            gemini_update_progress($processId, 55, 'processing', "Created {$totalChunks} chunks. Starting AI extraction...");

            // Process each chunk
            foreach ($chunkFiles as $index => $chunkFile) {
                $chunkNumber = $index + 1;

                // Calculate progress for this chunk (55% to 85% range)
                $chunkProgress = 55 + (($index / $totalChunks) * 30);
                gemini_update_progress($processId, $chunkProgress, 'extracting', "Processing chunk {$chunkNumber} of {$totalChunks}...");

                log_message('info', "Processing chunk {$chunkNumber}/{$totalChunks}: " . basename($chunkFile));

                $chunkResult = gemini_extract_text_direct($chunkFile, 'application/pdf', $apiKey, $baseUrl, $model);

                if ($chunkResult['success']) {
                    $chunkText = $chunkResult['extracted_text'];
                    $allExtractedTexts[] = "## Document Part {$chunkNumber} of {$totalChunks}\n\n" . $chunkText;
                    $totalTextLength += strlen($chunkText);
                    $totalWordCount += str_word_count($chunkText);

                    log_message('info', "Chunk {$chunkNumber} processed successfully - " . strlen($chunkText) . " characters");
                } else {
                    log_message('error', "Failed to process chunk {$chunkNumber}: " . $chunkResult['message']);
                    $allExtractedTexts[] = "## Document Part {$chunkNumber} of {$totalChunks}\n\n**Error extracting text from this section:** " . $chunkResult['message'];
                }

                // Clean up chunk file
                if (file_exists($chunkFile)) {
                    unlink($chunkFile);
                }
            }

            // Combine all extracted texts
            gemini_update_progress($processId, 85, 'combining', 'Combining extracted texts...');

            $combinedText = "# Complete Document Text Extraction\n\n";
            $combinedText .= "**Document:** " . basename($filePath) . "\n";
            $combinedText .= "**Total Pages:** {$totalPages}\n";
            $combinedText .= "**Processed in:** " . count($chunkFiles) . " chunks\n\n";
            $combinedText .= "---\n\n";
            $combinedText .= implode("\n\n---\n\n", $allExtractedTexts);

            gemini_update_progress($processId, 90, 'completed', 'Text extraction completed successfully!');

            return [
                'success' => true,
                'extracted_text' => $combinedText,
                'file_info' => [
                    'name' => basename($filePath),
                    'size' => filesize($filePath),
                    'type' => 'application/pdf',
                    'total_pages' => $totalPages,
                    'chunks_processed' => count($chunkFiles),
                    'text_length' => strlen($combinedText),
                    'word_count' => str_word_count($combinedText)
                ]
            ];

        } catch (Exception $e) {
            log_message('error', 'Error in PDF splitting extraction: ' . $e->getMessage());
            gemini_update_progress($processId, 0, 'error', 'Error processing PDF: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error processing PDF with splitting: ' . $e->getMessage()
            ];
        }
    }
}

if (!function_exists('gemini_update_progress')) {
    /**
     * Update progress for file processing
     *
     * @param string $processId Process ID for tracking
     * @param int $progress Progress percentage (0-100)
     * @param string $step Current step name
     * @param string $message Progress message
     */
    function gemini_update_progress($processId, $progress, $step, $message)
    {
        if (!$processId) return;

        // Get current file name from session
        $currentProgress = session()->get("upload_progress_{$processId}");
        $fileName = $currentProgress['file_name'] ?? 'Unknown file';

        session()->set("upload_progress_{$processId}", [
            'step' => $step,
            'progress' => $progress,
            'message' => $message,
            'file_name' => $fileName
        ]);
    }
}

if (!function_exists('gemini_split_pdf_into_chunks')) {
    /**
     * Split PDF into smaller chunks of specified page count
     *
     * @param string $filePath Full path to the PDF file
     * @param int $totalPages Total number of pages in the PDF
     * @param int $maxPagesPerChunk Maximum pages per chunk (default 10)
     * @return array Response with chunk file paths or error
     */
    function gemini_split_pdf_into_chunks($filePath, $totalPages, $maxPagesPerChunk = 10)
    {
        try {
            // Load composer autoloader
            $vendorPath = __DIR__ . '/../../vendor/autoload.php';
            if (file_exists($vendorPath)) {
                require_once $vendorPath;
            } else {
                // Fallback for different directory structures
                require_once dirname(__DIR__, 2) . '/vendor/autoload.php';
            }

            $chunkFiles = [];
            $chunksNeeded = ceil($totalPages / $maxPagesPerChunk);

            // Create temporary directory for chunks
            $tempDir = sys_get_temp_dir() . '/pdf_chunks_' . uniqid();
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            for ($chunkIndex = 0; $chunkIndex < $chunksNeeded; $chunkIndex++) {
                $startPage = ($chunkIndex * $maxPagesPerChunk) + 1;
                $endPage = min($startPage + $maxPagesPerChunk - 1, $totalPages);

                $chunkFileName = $tempDir . '/chunk_' . ($chunkIndex + 1) . '_pages_' . $startPage . '_to_' . $endPage . '.pdf';

                // Create new PDF with FPDI
                $pdf = new \setasign\Fpdi\Fpdi();
                $pageCount = $pdf->setSourceFile($filePath);

                // Add pages to chunk
                for ($pageNum = $startPage; $pageNum <= $endPage; $pageNum++) {
                    if ($pageNum <= $pageCount) {
                        $tplId = $pdf->importPage($pageNum);
                        $pdf->AddPage();
                        $pdf->useTemplate($tplId);
                    }
                }

                // Save chunk file
                $pdf->Output('F', $chunkFileName);
                $chunkFiles[] = $chunkFileName;

                log_message('info', "Created chunk " . ($chunkIndex + 1) . ": pages {$startPage}-{$endPage} -> " . basename($chunkFileName));
            }

            return [
                'success' => true,
                'chunk_files' => $chunkFiles,
                'total_chunks' => count($chunkFiles),
                'temp_dir' => $tempDir
            ];

        } catch (Exception $e) {
            log_message('error', 'Error splitting PDF: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error splitting PDF: ' . $e->getMessage()
            ];
        }
    }
}

if (!function_exists('gemini_extract_text_direct')) {
    /**
     * Direct text extraction from file (no splitting)
     *
     * @param string $filePath Full path to the file
     * @param string $mimeType MIME type of the file
     * @param string $apiKey Gemini API key
     * @param string $baseUrl Gemini API base URL
     * @param string $model Gemini model name
     * @param string $processId Optional process ID for progress tracking
     * @return array Response with extracted text or error
     */
    function gemini_extract_text_direct($filePath, $mimeType, $apiKey, $baseUrl, $model, $processId = null)
    {
        try {
            // Update progress
            gemini_update_progress($processId, 60, 'preparing', 'Preparing file for AI processing...');

            // Read and encode file
            $fileContent = file_get_contents($filePath);
            $base64Content = base64_encode($fileContent);

            // Create extraction prompt
            $prompt = "CRITICAL INSTRUCTIONS: Extract ALL text content from this document using your VISION CAPABILITIES to read complex layouts. Go through EVERY PAGE and extract COMPLETE text. Do NOT summarize or skip any content.

COMPLEX LAYOUT HANDLING:
- Use your VISION to carefully read and understand complex tables, diagrams, and layouts
- For complicated tables: Read each cell carefully and convert into clear sentences or structured lists
- For diagrams/charts: Describe all visible text, labels, data points, and relationships in plain language
- For forms with complex layouts: Extract field names and values in logical order
- For multi-column layouts: Read left-to-right, top-to-bottom, then reorganize into flowing text
- For repeated elements (dots, dashes, underscores): DO NOT repeat excessively - summarize patterns instead

For multi-page documents:
1. Process page 1 completely using vision capabilities
2. Process page 2 completely using vision capabilities
3. Continue until ALL pages are processed
4. Extract EVERYTHING - headers, footers, body text, tables, captions

AVOID REPETITION ERRORS:
- If you see repeated dots (.....), convert to 'dotted line' or similar description
- If you see repeated dashes (-----), convert to 'line separator' or similar
- If you see repeated underscores (____), convert to 'blank field' or 'fill-in space'
- Do not reproduce excessive repetitive characters that serve formatting purposes
- Focus on meaningful text content, not decorative elements

Format the output in clean markdown:
- Use # for main headings
- Use ## for subheadings
- Use **bold** for emphasis
- Use bullet points for lists
- Convert complex tables into readable sentences or simple lists
- Prioritize readability over exact layout preservation

IMPORTANT: This is a complete text extraction task using AI vision. Extract EVERYTHING from EVERY page intelligently and make it readable.";

            // Prepare request data
            $requestData = [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'inline_data' => [
                                    'mime_type' => $mimeType,
                                    'data' => $base64Content
                                ]
                            ],
                            [
                                'text' => $prompt
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.1,
                    'topK' => 32,
                    'topP' => 0.95,
                    'maxOutputTokens' => 8192,
                    'responseMimeType' => 'text/plain'
                ]
            ];

            // Update progress
            gemini_update_progress($processId, 70, 'extracting', 'AI is extracting text from document...');

            // Make API request
            $url = $baseUrl . "/models/{$model}:generateContent?key=" . $apiKey;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 240); // 4 minutes timeout
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_error($ch)) {
                curl_close($ch);
                gemini_update_progress($processId, 0, 'error', 'Network error: ' . curl_error($ch));
                return [
                    'success' => false,
                    'message' => 'Network error: ' . curl_error($ch)
                ];
            }

            curl_close($ch);

            // Parse response
            $responseData = json_decode($response, true);

            if ($statusCode !== 200 || empty($responseData) || isset($responseData['error'])) {
                $errorMessage = isset($responseData['error']['message'])
                    ? $responseData['error']['message']
                    : 'Unknown error with Gemini API';

                gemini_update_progress($processId, 0, 'error', 'AI processing error: ' . $errorMessage);
                return [
                    'success' => false,
                    'message' => 'Error from Gemini API: ' . $errorMessage
                ];
            }

            // Extract text from response
            if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                $extractedText = $responseData['candidates'][0]['content']['parts'][0]['text'];

                gemini_update_progress($processId, 85, 'completed', 'Text extraction completed successfully!');

                return [
                    'success' => true,
                    'extracted_text' => $extractedText,
                    'file_info' => [
                        'name' => basename($filePath),
                        'size' => filesize($filePath),
                        'type' => $mimeType,
                        'text_length' => strlen($extractedText),
                        'word_count' => str_word_count($extractedText)
                    ]
                ];
            }

            gemini_update_progress($processId, 0, 'error', 'No text content received from AI');
            return [
                'success' => false,
                'message' => 'No text content received from Gemini API'
            ];

        } catch (Exception $e) {
            gemini_update_progress($processId, 0, 'error', 'Processing error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error processing file: ' . $e->getMessage()
            ];
        }
    }
}

if (!function_exists('gemini_analyze_applicant')) {
    /**
     * Analyze applicant data using Gemini AI
     *
     * @param array $applicantData The applicant data to analyze
     * @param string $prompt The analysis prompt
     * @return array Response from Gemini AI
     */
    function gemini_analyze_applicant($applicantData, $prompt)
    {
        // Gemini API configuration
        $apiKey = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        $baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        $model = 'gemini-2.0-flash';

        if (empty($apiKey)) {
            return [
                'success' => false,
                'message' => 'Gemini API key not configured'
            ];
        }

        // Prepare the analysis data
        $analysisText = "APPLICANT ANALYSIS REQUEST\n\n";
        $analysisText .= "Position Applied For: " . ($applicantData['position_title'] ?? 'Not specified') . "\n";
        $analysisText .= "Applicant Name: " . ($applicantData['name'] ?? 'Not provided') . "\n\n";

        // Add personal information
        if (!empty($applicantData['personal_info'])) {
            $analysisText .= "PERSONAL INFORMATION:\n";
            foreach ($applicantData['personal_info'] as $key => $value) {
                if (!empty($value)) {
                    $analysisText .= "- " . ucwords(str_replace('_', ' ', $key)) . ": " . $value . "\n";
                }
            }
            $analysisText .= "\n";
        }

        // Add education information
        if (!empty($applicantData['education'])) {
            $analysisText .= "EDUCATION:\n";
            foreach ($applicantData['education'] as $index => $edu) {
                $analysisText .= "Education " . ($index + 1) . ":\n";
                $analysisText .= "- Institution: " . ($edu['institution'] ?? 'Not specified') . "\n";
                $analysisText .= "- Qualification: " . ($edu['qualification'] ?? 'Not specified') . "\n";
                $analysisText .= "- Field of Study: " . ($edu['field_of_study'] ?? 'Not specified') . "\n";
                $analysisText .= "- Year Completed: " . ($edu['year_completed'] ?? 'Not specified') . "\n";
                $analysisText .= "- Grade/GPA: " . ($edu['grade'] ?? 'Not specified') . "\n\n";
            }
        }

        // Add work experience
        if (!empty($applicantData['experience'])) {
            $analysisText .= "WORK EXPERIENCE:\n";
            foreach ($applicantData['experience'] as $index => $exp) {
                $analysisText .= "Experience " . ($index + 1) . ":\n";
                $analysisText .= "- Company: " . ($exp['company'] ?? 'Not specified') . "\n";
                $analysisText .= "- Position: " . ($exp['position'] ?? 'Not specified') . "\n";
                $analysisText .= "- Duration: " . ($exp['start_date'] ?? 'Not specified') . " to " . ($exp['end_date'] ?? 'Present') . "\n";
                $analysisText .= "- Responsibilities: " . ($exp['responsibilities'] ?? 'Not specified') . "\n\n";
            }
        }

        // Add skills
        if (!empty($applicantData['skills'])) {
            $analysisText .= "SKILLS:\n";
            foreach ($applicantData['skills'] as $skill) {
                $analysisText .= "- " . $skill . "\n";
            }
            $analysisText .= "\n";
        }

        // Add certifications
        if (!empty($applicantData['certifications'])) {
            $analysisText .= "CERTIFICATIONS:\n";
            foreach ($applicantData['certifications'] as $cert) {
                $analysisText .= "- " . $cert . "\n";
            }
            $analysisText .= "\n";
        }

        // Add languages
        if (!empty($applicantData['languages'])) {
            $analysisText .= "LANGUAGES:\n";
            foreach ($applicantData['languages'] as $lang) {
                $analysisText .= "- " . $lang . "\n";
            }
            $analysisText .= "\n";
        }

        // Add references
        if (!empty($applicantData['references'])) {
            $analysisText .= "REFERENCES:\n";
            foreach ($applicantData['references'] as $index => $ref) {
                $analysisText .= "Reference " . ($index + 1) . ":\n";
                $analysisText .= "- Name: " . ($ref['name'] ?? 'Not specified') . "\n";
                $analysisText .= "- Position: " . ($ref['position'] ?? 'Not specified') . "\n";
                $analysisText .= "- Company: " . ($ref['company'] ?? 'Not specified') . "\n";
                $analysisText .= "- Contact: " . ($ref['contact'] ?? 'Not specified') . "\n\n";
            }
        }

        // Add extracted text from documents if available
        if (!empty($applicantData['extracted_texts'])) {
            $analysisText .= "DOCUMENT CONTENT:\n";
            foreach ($applicantData['extracted_texts'] as $filename => $text) {
                $analysisText .= "From file: " . $filename . "\n";
                $analysisText .= $text . "\n\n";
            }
        }

        // Prepare the request data
        $requestData = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt . "\n\nAPPLICANT DATA TO ANALYZE:\n\n" . $analysisText
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.3,
                'topK' => 32,
                'topP' => 0.95,
                'maxOutputTokens' => 4096,
                'responseMimeType' => 'text/plain'
            ]
        ];

        // Make the API request
        $url = $baseUrl . "/models/{$model}:generateContent?key=" . $apiKey;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_error($ch)) {
            curl_close($ch);
            return [
                'success' => false,
                'message' => 'Network error: ' . curl_error($ch)
            ];
        }

        curl_close($ch);

        // Parse the response
        $responseData = json_decode($response, true);

        if ($statusCode !== 200 || empty($responseData) || isset($responseData['error'])) {
            $errorMessage = isset($responseData['error']['message'])
                ? $responseData['error']['message']
                : 'Unknown error with Gemini API';

            log_message('error', 'Gemini API Response Error: ' . $errorMessage);

            return [
                'success' => false,
                'message' => 'Error from Gemini API: ' . $errorMessage
            ];
        }

        // Check if we have text in the response
        if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'analysis' => $responseData['candidates'][0]['content']['parts'][0]['text']
            ];
        }

        return [
            'success' => false,
            'message' => 'No analysis content received from Gemini API'
        ];
    }
}