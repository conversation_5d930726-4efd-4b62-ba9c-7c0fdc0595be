<?php

// Simple verification script to check if the user was created
require_once 'vendor/autoload.php';

use App\Models\DakoiiUsersModel;

// Initialize CodeIgniter for CLI
$paths = new Config\Paths();
require $paths->systemDirectory . '/Boot.php';

// Set environment to CLI
$_SERVER['REQUEST_METHOD'] = 'CLI';
$_SERVER['argv'] = ['verify_user.php'];
$_SERVER['argc'] = 1;

\CodeIgniter\Boot::bootSpark($paths);

// Get the user model
$userModel = new DakoiiUsersModel();

// Check if the user exists
$user = $userModel->getUserByUsername('fkenny');

if ($user) {
    echo "✅ User verification successful!\n";
    echo "ID: " . $user['id'] . "\n";
    echo "Name: " . $user['name'] . "\n";
    echo "Username: " . $user['username'] . "\n";
    echo "Role: " . $user['role'] . "\n";
    echo "Orgcode: " . $user['orgcode'] . "\n";
    echo "Is Active: " . ($user['is_active'] ? 'Yes' : 'No') . "\n";
    echo "Created At: " . $user['created_at'] . "\n";
    
    // Test password verification
    if (password_verify('dakoii', $user['password'])) {
        echo "✅ Password verification successful!\n";
    } else {
        echo "❌ Password verification failed!\n";
    }
} else {
    echo "❌ User not found!\n";
}
